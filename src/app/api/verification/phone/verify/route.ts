import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// This should match the storage from send/route.ts
// In production, use Redis or similar
const verificationCodes = new Map<string, { code: string; expires: Date; phoneNumber: string }>();

// POST endpoint to verify phone number
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { phoneNumber, code } = await request.json();

    if (!phoneNumber || !code) {
      return NextResponse.json(
        { message: 'Phone number and verification code are required' },
        { status: 400 }
      );
    }

    // Get stored verification data
    const storedData = verificationCodes.get(session.user.id);

    if (!storedData) {
      return NextResponse.json(
        { message: 'No verification code found. Please request a new code.' },
        { status: 400 }
      );
    }

    // Check if code has expired
    if (new Date() > storedData.expires) {
      verificationCodes.delete(session.user.id);
      return NextResponse.json(
        { message: 'Verification code has expired. Please request a new code.' },
        { status: 400 }
      );
    }

    // Check if phone number matches
    if (storedData.phoneNumber !== phoneNumber) {
      return NextResponse.json(
        { message: 'Phone number does not match the one used to send the code.' },
        { status: 400 }
      );
    }

    // Check if code matches
    if (storedData.code !== code) {
      return NextResponse.json(
        { message: 'Invalid verification code.' },
        { status: 400 }
      );
    }

    // Code is valid, update user's phone number and mark as verified
    await prisma.user.update({
      where: { id: session.user.id },
      data: {
        phone: phoneNumber,
        // You might want to add a phoneVerified field to track this separately
      },
    });

    // Clean up the verification code
    verificationCodes.delete(session.user.id);

    return NextResponse.json({
      message: 'Phone number verified successfully',
    });
  } catch (error) {
    console.error('Error verifying phone number:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
