import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';

// This is a mock function for ID verification
// In a real application, you would use a service like Jumio, Onfido, or similar
async function verifyIdentityDocument(documentUrl: string, documentType: string): Promise<boolean> {
  console.log(`Verifying ${documentType} document at ${documentUrl}`);
  // In a real implementation, this would call an ID verification service API
  // For this mock, we'll just return true after a delay to simulate processing
  await new Promise(resolve => setTimeout(resolve, 1000));
  return true;
}

// POST endpoint to submit ID for verification
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { documentType, frontImageUrl, backImageUrl } = await request.json();

    if (!documentType || !frontImageUrl) {
      return NextResponse.json(
        { message: 'Document type and front image are required' },
        { status: 400 }
      );
    }

    // Check if user already has a pending or approved verification
    const existingVerification = await prisma.idVerification.findFirst({
      where: {
        userId: session.user.id,
        status: {
          in: ['PENDING', 'APPROVED'],
        },
      },
    });

    if (existingVerification) {
      return NextResponse.json(
        { message: 'You already have a verification request in progress' },
        { status: 400 }
      );
    }

    // Create verification request
    const verification = await prisma.idVerification.create({
      data: {
        userId: session.user.id,
        documentType,
        frontImageUrl,
        backImageUrl,
        status: 'PENDING',
        submittedAt: new Date(),
      },
    });

    return NextResponse.json({
      message: 'ID verification submitted successfully',
      verificationId: verification.id,
      status: 'PENDING',
    });
  } catch (error) {
    console.error('Error submitting ID verification:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint to check verification status
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const verification = await prisma.idVerification.findFirst({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        submittedAt: 'desc',
      },
    });

    return NextResponse.json(verification);
  } catch (error) {
    console.error('Error fetching verification status:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Admin endpoint to approve or reject ID verification
export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { sellerId, status, notes } = await request.json();

    if (!sellerId || !status) {
      return NextResponse.json(
        { message: 'Seller ID and status are required' },
        { status: 400 }
      );
    }

    if (status !== 'VERIFIED' && status !== 'REJECTED') {
      return NextResponse.json(
        { message: 'Status must be VERIFIED or REJECTED' },
        { status: 400 }
      );
    }

    // Update seller's ID verification status
    const updatedSeller = await prisma.seller.update({
      where: { id: sellerId },
      data: {
        idVerificationStatus: status,
        idVerified: status === 'VERIFIED',
        idVerificationDate: status === 'VERIFIED' ? new Date() : null,
      },
    });

    // Create a notification for the seller
    await prisma.notification.create({
      data: {
        type: 'ID_VERIFICATION',
        message: status === 'VERIFIED'
          ? 'Your ID has been verified successfully!'
          : `Your ID verification was rejected. Reason: ${notes || 'Not specified'}`,
        userId: updatedSeller.userId,
      },
    });

    return NextResponse.json({
      message: `ID verification ${status.toLowerCase()}`,
      seller: {
        id: updatedSeller.id,
        idVerified: updatedSeller.idVerified,
        idVerificationStatus: updatedSeller.idVerificationStatus,
      },
    });
  } catch (error) {
    console.error('Error updating ID verification status:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
